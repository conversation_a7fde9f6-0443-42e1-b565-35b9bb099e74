# Scripts de Administração - MultiAgenda

Este diretório contém scripts para gerenciar o usuário administrador do sistema MultiAgenda.

## 📁 Scripts Disponíveis

### 1. `init-admin.js` - Inicialização Automática
**Executado automaticamente** quando o servidor inicia.

**Função:**
- Cria o usuário administrador inicial se não existir
- Atualiza a senha do admin se as variáveis de ambiente mudaram
- Garante que sempre existe um admin no sistema

**Configuração via variáveis de ambiente (.env):**
```env
ADMIN_USERNAME=admin          # Username do administrador
ADMIN_PASSWORD=123456         # Senha do administrador  
ADMIN_EMAIL=<EMAIL>  # Email do administrador
ADMIN_NAME=Administrador      # Nome completo do administrador
```

### 2. `change-admin.js` - Alteração Manual
**Executado manualmente** quando você quiser alterar as credenciais.

**Como usar:**
```bash
# Dentro do container da API
node scripts/change-admin.js

# Ou via Docker
docker exec -it multiagenda-api node scripts/change-admin.js
```

**Funcionalidades:**
- Interface interativa para alterar credenciais
- Permite alterar username, nome, email e senha
- Confirmação antes de aplicar alterações
- Validação de dados de entrada

## 🔧 Como Personalizar o Administrador

### Método 1: Variáveis de Ambiente (Recomendado)
1. Edite o arquivo `api/.env`
2. Altere as variáveis `ADMIN_*` conforme desejado
3. Reinicie o container da API
4. O sistema atualizará automaticamente as credenciais

### Método 2: Script Interativo
1. Execute o script de alteração:
   ```bash
   docker exec -it multiagenda-api node scripts/change-admin.js
   ```
2. Siga as instruções na tela
3. Confirme as alterações

## 🚀 Exemplos de Uso

### Alterar apenas a senha via .env
```env
ADMIN_PASSWORD=minha_nova_senha_super_segura
```

### Criar admin personalizado via .env
```env
ADMIN_USERNAME=meuadmin
ADMIN_PASSWORD=senha123!@#
ADMIN_EMAIL=<EMAIL>
ADMIN_NAME=João Silva
```

### Usar script interativo
```bash
docker exec -it multiagenda-api node scripts/change-admin.js
```

## ⚠️ Notas Importantes

1. **Segurança:** Sempre use senhas fortes em produção
2. **Backup:** Faça backup do banco antes de alterações importantes
3. **Reinicialização:** Reinicie o servidor após alterações no .env
4. **Logs:** Verifique os logs do container para confirmar as alterações

## 🔍 Verificação

Para verificar se o admin foi criado/alterado corretamente:

```bash
# Testar login via API
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'
```

Se retornar um token JWT, o admin está funcionando corretamente.

## 🐛 Solução de Problemas

### Admin não foi criado
- Verifique os logs do container: `docker logs multiagenda-api`
- Confirme se o banco de dados está acessível
- Verifique se as variáveis de ambiente estão corretas

### Não consigo fazer login
- Confirme as credenciais no .env
- Execute o script de alteração para redefinir
- Verifique se não há caracteres especiais problemáticos

### Script não executa
- Confirme se está dentro do container da API
- Verifique se o Node.js tem acesso ao banco de dados
- Confirme se os módulos estão instalados corretamente
