#!/usr/bin/env node

const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');
const User = require('../models/User');
const readline = require('readline');

// Interface para entrada do usuário
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

function questionHidden(prompt) {
  return new Promise((resolve) => {
    process.stdout.write(prompt);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let input = '';
    process.stdin.on('data', function(char) {
      char = char + '';
      
      switch (char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdout.write('\n');
          resolve(input);
          break;
        case '\u0003':
          process.exit();
          break;
        case '\u007f': // backspace
          if (input.length > 0) {
            input = input.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          input += char;
          process.stdout.write('*');
          break;
      }
    });
  });
}

async function changeAdminCredentials() {
  try {
    console.log('🔧 Alteração de Credenciais do Administrador');
    console.log('='.repeat(50));
    
    // Conectar ao banco
    await sequelize.authenticate();
    console.log('✅ Conectado ao banco de dados\n');
    
    // Buscar admin atual
    const currentAdmin = await User.findOne({ 
      where: { role: 'admin' },
      order: [['createdAt', 'ASC']]
    });
    
    if (!currentAdmin) {
      console.log('❌ Nenhum administrador encontrado no sistema');
      console.log('Execute o servidor primeiro para criar o admin inicial');
      return;
    }
    
    console.log('👤 Administrador atual:');
    console.log(`   Username: ${currentAdmin.username}`);
    console.log(`   Nome: ${currentAdmin.name}`);
    console.log(`   Email: ${currentAdmin.email}\n`);
    
    // Perguntar se quer alterar
    const shouldChange = await question('Deseja alterar as credenciais? (s/N): ');
    if (shouldChange.toLowerCase() !== 's' && shouldChange.toLowerCase() !== 'sim') {
      console.log('Operação cancelada');
      return;
    }
    
    console.log('\n📝 Digite as novas credenciais (deixe em branco para manter atual):');
    
    // Coletar novos dados
    const newUsername = await question(`Username [${currentAdmin.username}]: `);
    const newName = await question(`Nome [${currentAdmin.name}]: `);
    const newEmail = await question(`Email [${currentAdmin.email}]: `);
    const newPassword = await questionHidden('Nova senha (deixe em branco para manter): ');
    
    // Preparar dados para atualização
    const updateData = {};
    if (newUsername.trim()) updateData.username = newUsername.trim();
    if (newName.trim()) updateData.name = newName.trim();
    if (newEmail.trim()) updateData.email = newEmail.trim();
    if (newPassword.trim()) {
      updateData.password = await bcrypt.hash(newPassword.trim(), 12);
    }
    
    if (Object.keys(updateData).length === 0) {
      console.log('\nNenhuma alteração foi feita');
      return;
    }
    
    // Confirmar alterações
    console.log('\n📋 Resumo das alterações:');
    if (updateData.username) console.log(`   Username: ${currentAdmin.username} → ${updateData.username}`);
    if (updateData.name) console.log(`   Nome: ${currentAdmin.name} → ${updateData.name}`);
    if (updateData.email) console.log(`   Email: ${currentAdmin.email} → ${updateData.email}`);
    if (updateData.password) console.log('   Senha: será alterada');
    
    const confirm = await question('\nConfirmar alterações? (s/N): ');
    if (confirm.toLowerCase() !== 's' && confirm.toLowerCase() !== 'sim') {
      console.log('Operação cancelada');
      return;
    }
    
    // Aplicar alterações
    updateData.updatedAt = new Date();
    await currentAdmin.update(updateData);
    
    console.log('\n✅ Credenciais do administrador atualizadas com sucesso!');
    console.log('\n📋 Novas credenciais:');
    console.log(`   Username: ${updateData.username || currentAdmin.username}`);
    console.log(`   Nome: ${updateData.name || currentAdmin.name}`);
    console.log(`   Email: ${updateData.email || currentAdmin.email}`);
    if (updateData.password) {
      console.log('   Senha: alterada');
    }
    
    console.log('\n💡 Dica: Reinicie o servidor para garantir que as alterações sejam aplicadas');
    
  } catch (error) {
    console.error('\n❌ Erro ao alterar credenciais:', error.message);
    throw error;
  } finally {
    rl.close();
    await sequelize.close();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  changeAdminCredentials()
    .then(() => {
      console.log('\n🎉 Processo concluído');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Falha na operação:', error.message);
      process.exit(1);
    });
}

module.exports = changeAdminCredentials;
