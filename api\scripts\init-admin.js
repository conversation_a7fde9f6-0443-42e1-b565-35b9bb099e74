const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');
const User = require('../models/User');

async function initializeAdmin() {
  try {
    console.log('🔧 Inicializando usuário administrador...');
    
    // Aguardar conexão com o banco
    await sequelize.authenticate();
    console.log('✅ Conectado ao banco de dados');
    
    // Sincronizar modelos (criar tabelas se não existirem)
    await sequelize.sync();
    console.log('✅ Modelos sincronizados');
    
    // Verificar se já existe um admin
    const existingAdmin = await User.findOne({ 
      where: { 
        username: process.env.ADMIN_USERNAME || 'admin' 
      } 
    });
    
    if (existingAdmin) {
      console.log('ℹ️ Usuário administrador já existe');
      
      // Verificar se precisa atualizar a senha (se a variável de ambiente mudou)
      const adminPassword = process.env.ADMIN_PASSWORD || '123456';
      const isPasswordValid = await bcrypt.compare(adminPassword, existingAdmin.password);
      
      if (!isPasswordValid) {
        console.log('🔄 Atualizando senha do administrador...');
        const hashedPassword = await bcrypt.hash(adminPassword, 12);
        
        // Atualizar diretamente no banco para evitar hooks
        await sequelize.query(`
          UPDATE "User" 
          SET password = :password, "updatedAt" = NOW() 
          WHERE username = :username
        `, {
          replacements: { 
            password: hashedPassword, 
            username: existingAdmin.username 
          }
        });
        
        console.log('✅ Senha do administrador atualizada');
      }
      
      return;
    }
    
    // Criar novo usuário administrador
    console.log('👤 Criando usuário administrador...');
    
    const adminUsername = process.env.ADMIN_USERNAME || 'admin';
    const adminPassword = process.env.ADMIN_PASSWORD || '123456';
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminName = process.env.ADMIN_NAME || 'Administrador';
    
    // Criar hash da senha
    const hashedPassword = await bcrypt.hash(adminPassword, 12);
    
    // Inserir diretamente no banco para evitar problemas com hooks
    await sequelize.query(`
      INSERT INTO "User" (username, password, name, email, role, "createdAt", "updatedAt")
      VALUES (:username, :password, :name, :email, 'admin', NOW(), NOW())
    `, {
      replacements: {
        username: adminUsername,
        password: hashedPassword,
        name: adminName,
        email: adminEmail
      }
    });
    
    console.log('✅ Usuário administrador criado com sucesso!');
    console.log('📋 Credenciais do administrador:');
    console.log(`   Username: ${adminUsername}`);
    console.log(`   Senha: ${adminPassword}`);
    console.log(`   Email: ${adminEmail}`);
    console.log(`   Nome: ${adminName}`);
    
  } catch (error) {
    console.error('❌ Erro ao inicializar administrador:', error.message);
    console.error('Stack:', error.stack);
    throw error;
  }
}

module.exports = initializeAdmin;

// Se executado diretamente
if (require.main === module) {
  initializeAdmin()
    .then(() => {
      console.log('🎉 Inicialização do administrador concluída');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na inicialização:', error.message);
      process.exit(1);
    });
}
