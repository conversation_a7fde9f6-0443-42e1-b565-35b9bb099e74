<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Multiagenda - Acesso</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #4CAF50, #2E7D32);
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    .login-container {
      background: white;
      padding: 40px;
      border-radius: 15px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      width: 100%;
      max-width: 350px;
      transition: all 0.3s ease;
    }
    .login-container:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    }
    .login-header {
      text-align: center;
      margin-bottom: 30px;
    }
    .login-container h2 {
      margin: 0 0 10px 0;
      color: #2E7D32;
      font-size: 28px;
    }
    .login-container p {
      color: #666;
      margin: 0;
      font-size: 16px;
    }
    .input-group {
      position: relative;
      margin-bottom: 25px;
    }
    .input-group i {
      position: absolute;
      left: 15px;
      top: 15px;
      color: #999;
    }
    .login-container input {
      width: 100%;
      padding: 15px 15px 15px 45px;
      border-radius: 8px;
      border: 1px solid #ddd;
      font-size: 16px;
      transition: all 0.3s ease;
      box-sizing: border-box;
    }
    .login-container input:focus {
      border-color: #4CAF50;
      box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
      outline: none;
    }
    .login-container input::placeholder {
      color: #aaa;
    }
    .login-container input.error {
      border-color: #f44336;
      animation: shake 0.3s ease-in-out;
    }
    .login-container button {
      width: 100%;
      padding: 15px;
      background: #4CAF50;
      color: white;
      font-weight: bold;
      font-size: 16px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 10px;
    }
    .login-container button:hover {
      background: #3d8b40;
      transform: translateY(-2px);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }
    .login-container button:active {
      transform: translateY(0);
    }
    .login-container button:disabled {
      background: #cccccc;
      cursor: not-allowed;
    }
    #chatApp {
      display: none;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    .error-message {
      color: #f44336;
      margin: 10px 0;
      text-align: center;
      font-size: 14px;
      display: none;
    }
    .loading {
      display: none;
      text-align: center;
      margin-top: 10px;
    }
    .loading-spinner {
      border: 3px solid #f3f3f3;
      border-top: 3px solid #4CAF50;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
      display: inline-block;
      margin-right: 10px;
      vertical-align: middle;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
      20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
  </style>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
  <div id="loginApp" class="login-container">
    <div class="login-header">
      <h2>MultiAgenda</h2>
      <p>Acesse sua conta</p>
    </div>
    <form id="loginForm" onsubmit="event.preventDefault(); login();">
      <div class="input-group">
        <i class="fas fa-user" aria-label="Ícone de usuário"></i>
        <input
          type="text"
          id="username"
          placeholder="Nome de usuário"
          required
          aria-describedby="errorMessage"
        />
      </div>
      <div class="input-group">
        <i class="fas fa-lock" aria-label="Ícone de senha"></i>
        <input
          type="password"
          id="password"
          placeholder="Senha"
          required
          aria-describedby="errorMessage"
        />
      </div>
      <div id="errorMessage" class="error-message"></div>
      <button type="submit">
        <span id="loginText">Entrar</span>
        <div id="loadingSpinner" class="loading">
          <span class="loading-spinner"></span>
          <span>Conectando...</span>
        </div>
      </button>
    </form>
  </div>

  <iframe id="chatApp" frameborder="0"></iframe>

  <script>
    // Detectar ambiente e configurar API URL
    const isLocalhost = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname.startsWith('192.168.');

    // Detectar se está rodando em container (porta 3005) ou diretamente (outras portas)
    const isInContainer = window.location.port === '3005';

    let API_BASE_URL;
    if (isLocalhost && !isInContainer) {
      // Rodando diretamente no host
      API_BASE_URL = 'http://localhost:8000/api';
    } else if (isLocalhost && isInContainer) {
      // Rodando em container Docker, mas acessado via localhost
      API_BASE_URL = 'http://localhost:8000/api';
    } else {
      // Produção
      API_BASE_URL = 'http://multiagenda.uniqsolutions.com.br:8000/api';
    }
    const AUTH_API_URL = `${API_BASE_URL}/auth/login`;
    const VERIFY_TOKEN_URL = `${API_BASE_URL}/auth/verify-token`;

    async function login() {
      const username = document.getElementById('username').value.trim();
      const password = document.getElementById('password').value.trim();
      const errorElement = document.getElementById('errorMessage');
      const loginButton = document.querySelector('button');
      const loginText = document.getElementById('loginText');
      const loadingSpinner = document.getElementById('loadingSpinner');

      // Resetar mensagens de erro
      errorElement.style.display = 'none';
      errorElement.textContent = '';
      document.getElementById('username').classList.remove('error');
      document.getElementById('password').classList.remove('error');

      if (!username || !password) {
        errorElement.textContent = 'Por favor, preencha usuário e senha.';
        errorElement.style.display = 'block';
        if (!username) document.getElementById('username').classList.add('error');
        if (!password) document.getElementById('password').classList.add('error');
        return;
      }

      // Mostrar estado de loading
      loginButton.disabled = true;
      loginText.style.display = 'none';
      loadingSpinner.style.display = 'block';

      try {
        console.log('Fazendo login em:', AUTH_API_URL);
        console.log('Hostname atual:', window.location.hostname);
        console.log('URL completa:', window.location.href);
        
        const response = await fetch(AUTH_API_URL, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          },
          body: JSON.stringify({ username, password }),
          mode: 'cors' // Forçar CORS
        });

        console.log('Status da resposta:', response.status);
        console.log('Headers da resposta:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          throw new Error(`Erro HTTP: ${response.status} - ${response.statusText}`);
        }

        let data;
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          data = await response.json();
        } else {
          throw new Error('Resposta do servidor não é JSON válido');
        }

        if (data.success) {
          localStorage.setItem('agendaToken', data.token);
          localStorage.setItem('agendaUser', username);
          localStorage.setItem('agendaUserData', JSON.stringify(data.user));

          document.getElementById('loginApp').style.opacity = '0';
          setTimeout(() => {
            document.getElementById('loginApp').style.display = 'none';
            const chatFrame = document.getElementById('chatApp');
            chatFrame.style.display = 'block';
            chatFrame.src = `chat.html?user=${encodeURIComponent(username)}&token=${encodeURIComponent(data.token)}`;
          }, 300);
        } else {
          errorElement.textContent = data.message || 'Credenciais inválidas. Tente novamente.';
          errorElement.style.display = 'block';
          loginButton.disabled = false;
          loginText.style.display = 'block';
          loadingSpinner.style.display = 'none';
        }
      } catch (error) {
        console.error('Erro na autenticação:', error);
        errorElement.textContent = 'Erro ao conectar com o servidor. Tente novamente mais tarde.';
        errorElement.style.display = 'block';
        loginButton.disabled = false;
        loginText.style.display = 'block';
        loadingSpinner.style.display = 'none';
      }
    }

    // Limpar erro ao digitar
    ['username', 'password'].forEach(id => {
      document.getElementById(id).addEventListener('input', function () {
        this.classList.remove('error');
        document.getElementById('errorMessage').style.display = 'none';
      });
    });

    // Autologin melhorado
    window.onload = async () => {
      const token = localStorage.getItem('agendaToken');
      const user = localStorage.getItem('agendaUser');
      const loginText = document.getElementById('loginText');
      const loadingSpinner = document.getElementById('loadingSpinner');

      // Limpar tentativas antigas ao carregar a página
      localStorage.removeItem('authAttempt');

      if (token && user) {
        console.log('Token encontrado, verificando autenticação...');
        loginText.style.display = 'none';
        loadingSpinner.style.display = 'block';

        try {
          console.log('Verificando token em:', VERIFY_TOKEN_URL);
          const response = await fetch(VERIFY_TOKEN_URL, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer ${token}`,
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            },
            mode: 'cors' // Forçar CORS
          });

          console.log('Status da verificação:', response.status);

          if (!response.ok) {
            throw new Error(`Erro HTTP: ${response.status}`);
          }

          let data;
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            data = await response.json();
          } else {
            throw new Error('Resposta do servidor não é JSON válido');
          }

          if (data.success) {
            console.log('Token válido, redirecionando para chat...');
            document.getElementById('loginApp').style.opacity = '0';
            setTimeout(() => {
              document.getElementById('loginApp').style.display = 'none';
              const chatFrame = document.getElementById('chatApp');
              chatFrame.style.display = 'block';
              chatFrame.src = `chat.html?user=${encodeURIComponent(user)}&token=${encodeURIComponent(token)}`;
            }, 300);
          } else {
            console.log('Token inválido, limpando dados...');
            localStorage.removeItem('agendaToken');
            localStorage.removeItem('agendaUser');
            localStorage.removeItem('agendaUserData');
            loginText.style.display = 'block';
            loadingSpinner.style.display = 'none';
          }
        } catch (error) {
          console.error('Erro ao verificar autenticação:', error);
          localStorage.removeItem('agendaToken');
          localStorage.removeItem('agendaUser');
          localStorage.removeItem('agendaUserData');
          loginText.style.display = 'block';
          loadingSpinner.style.display = 'none';
        }
      } else {
        console.log('Nenhum token encontrado, mostrando formulário de login');
        loginText.style.display = 'block';
        loadingSpinner.style.display = 'none';
      }
    };
  </script>
</body>
</html>