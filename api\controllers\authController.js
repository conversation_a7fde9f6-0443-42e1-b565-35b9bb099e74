const { Op } = require('sequelize');
const { sequelize } = require('../config/database');
const User = require('../models/User');
const { sanitizeString } = require('../middleware/validation');
const logger = require('../utils/logger');

// Contadores para limitar tentativas de login (implementação básica de rate limiting)
const loginAttempts = {};
const MAX_LOGIN_ATTEMPTS = 5;
const LOGIN_TIMEOUT = 15 * 60 * 1000; // 15 minutos

// @desc    Registrar usuário
// @route   POST /api/auth/register
// @access  Public
exports.register = async (req, res) => {
  try {
    const { username, name, email, password } = req.body;

    // Verificar se o usuário já existe
    const userExists = await User.findOne({
      where: {
        [Op.or]: [{ email }, { username }]
      }
    });
    if (userExists) {
      return res.status(400).json({
        success: false,
        message: '<PERSON>u<PERSON>rio ou email já existe'
      });
    }

    // Criar usuário
    const user = await User.create({
      username,
      name,
      email,
      password
    });

    logger.audit('USER_REGISTERED', user.id, {
      username: user.username,
      email: user.email,
      ip: req.ip
    });

    sendTokenResponse(user, 201, res);
  } catch (error) {
    logger.error('Erro no registro de usuário', {
      error: error.message,
      stack: error.stack,
      ip: req.ip,
      userAgent: req.get('user-agent')
    });

    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
};

// @desc    Login de usuário
// @route   POST /api/auth/login
// @access  Public
exports.login = async (req, res) => {
  try {
    const { username, password } = req.body;
    const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress;

    // Verificar se o usuário e senha foram fornecidos
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Por favor, forneça um usuário e senha'
      });
    }

    // Verificar tentativas de login
    if (loginAttempts[ipAddress] && loginAttempts[ipAddress].count >= MAX_LOGIN_ATTEMPTS) {
      const timeElapsed = Date.now() - loginAttempts[ipAddress].timestamp;
      if (timeElapsed < LOGIN_TIMEOUT) {
        logger.security('Tentativas excessivas de login bloqueadas', {
          ip: ipAddress,
          username,
          attempts: loginAttempts[ipAddress].count,
          userAgent: req.get('user-agent')
        });

        return res.status(429).json({
          success: false,
          message: `Muitas tentativas de login. Por favor, tente novamente em ${Math.ceil((LOGIN_TIMEOUT - timeElapsed) / 60000)} minutos.`
        });
      } else {
        // Reset após timeout
        loginAttempts[ipAddress] = { count: 1, timestamp: Date.now() };
      }
    }

    // Verificar se o usuário existe
    const user = await User.findOne({
      where: { username }
    });
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Credenciais inválidas'
      });
    }

    // Verificar se a senha está correta
    const isMatch = await user.matchPassword(password);
    if (!isMatch) {
      // Incrementar contagem de tentativas falhas
      if (!loginAttempts[ipAddress]) {
        loginAttempts[ipAddress] = { count: 1, timestamp: Date.now() };
      } else {
        loginAttempts[ipAddress].count++;
        loginAttempts[ipAddress].timestamp = Date.now();
      }
      
      return res.status(401).json({
        success: false,
        message: 'Credenciais inválidas'
      });
    }

    // Reset tentativas de login em caso de sucesso
    if (loginAttempts[ipAddress]) {
      delete loginAttempts[ipAddress];
    }

    sendTokenResponse(user, 200, res);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// @desc    Logout de usuário (apenas frontend)
// @route   POST /api/auth/logout
// @access  Private
exports.logout = async (req, res) => {
  res.status(200).json({
    success: true,
    data: {}
  });
};

// @desc    Obter usuário atual
// @route   GET /api/auth/me
// @access  Private
exports.getMe = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id);

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

// @desc    Verificar token
// @route   POST /api/auth/verify-token
// @access  Private
exports.verifyToken = (req, res) => {
  // Logs de segurança removidos para produção
  
  // Garantir que a resposta seja sempre JSON
  res.setHeader('Content-Type', 'application/json');
  
  res.status(200).json({
    success: true,
    message: 'Token válido',
    user: {
      id: req.user.id,
      username: req.user.username,
      name: req.user.name,
      email: req.user.email,
      role: req.user.role
    }
  });
};

// Função de envio de token com cookie
const sendTokenResponse = (user, statusCode, res) => {
  // Criar token
  const token = user.getSignedJwtToken();
  
  console.log('Token gerado para usuário:', user.username);
  console.log('ID do usuário:', user.id);

  const options = {
    expires: new Date(
      Date.now() + (process.env.JWT_COOKIE_EXPIRE || 30) * 24 * 60 * 60 * 1000
    ),
    httpOnly: true
  };

  if (process.env.NODE_ENV === 'production') {
    options.secure = true;
  }

  // Garantir que a resposta seja sempre JSON
  res.setHeader('Content-Type', 'application/json');

  res
    .status(statusCode)
    .json({
      success: true,
      token,
      user: {
        id: user.id,
        name: user.name,
        username: user.username,
        email: user.email,
        role: user.role
      },
      timestamp: new Date().toISOString()
    });
};
