const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const dotenv = require('dotenv');
const path = require('path');

// Importar sistema de logging
const logger = require('./utils/logger');

// Carrega variáveis de ambiente
dotenv.config();

// Configurar handlers de erro globais
logger.setupErrorHandlers();

// Importar middlewares de segurança
const {
  generalLimiter,
  blockMaliciousIPs,
  blockSuspiciousURLs,
  securityLogger
} = require('./middleware/security');

// Importar middleware de resposta JSON
const { ensureJsonResponse, jsonErrorHandler } = require('./middleware/jsonResponse');

// Importar configuração do banco
const { sequelize, testConnection, syncModels } = require('./config/database');

// Importar inicializador do admin
const initializeAdmin = require('./scripts/init-admin');

// Importar modelos e suas associações
const models = require('./models');

// Importar rotas
const authRoutes = require('./routes/authRoutes');
const chatRoutes = require('./routes/chatRoutes');
const appointmentRoutes = require('./routes/appointmentRoutes');

// Inicializar app
const app = express();

// Configurar trust proxy de forma mais restritiva
// Em produção, configure apenas para IPs específicos do proxy
if (process.env.NODE_ENV === 'production') {
  app.set('trust proxy', ['loopback', 'linklocal', 'uniquelocal']); 
} else {
  app.set('trust proxy', 1); // Para desenvolvimento local
}

// Middlewares de segurança
app.use(helmet({
  crossOriginEmbedderPolicy: false, // Permitir iframe para chat
  contentSecurityPolicy: false // Desabilitar CSP para evitar conflitos com frontend
}));

// Aplicar middlewares de segurança baseado no ambiente e configuração
if (process.env.SECURITY_LOGGING === 'true') {
  app.use(securityLogger);
}

if (process.env.BLOCK_MALICIOUS_IPS === 'true') {
  app.use(blockMaliciousIPs);
}

app.use(blockSuspiciousURLs);

// Rate limiting apenas em produção ou se explicitamente habilitado
if (process.env.NODE_ENV === 'production' || process.env.ENABLE_RATE_LIMITING === 'true') {
  app.use(generalLimiter);
}

// Middleware para garantir respostas JSON (inline)
app.use((req, res, next) => {
  // Override do método json para garantir header correto
  const originalJson = res.json;
  res.json = function(obj) {
    res.setHeader('Content-Type', 'application/json; charset=utf-8');
    return originalJson.call(this, obj);
  };
  next();
});

// Middlewares
app.use(express.json({ limit: '10mb' }));

// Middleware específico para tratar OPTIONS (preflight) requests
app.options('*', (req, res) => {
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Forwarded-For, X-Real-IP, X-Forwarded-Proto, Accept, Origin');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.sendStatus(200);
});

// Configurar CORS baseado no ambiente
const getAllowedOrigins = () => {
  if (process.env.NODE_ENV === 'production') {
    // Em produção, usar apenas origens específicas
    return process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : [];
  } else {
    // Em desenvolvimento, permitir localhost e 127.0.0.1 em várias portas
    const defaultOrigins = [
      'http://localhost:3000',
      'http://localhost:3005',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3005',
      'http://localhost:8080',
      'http://127.0.0.1:8080'
    ];

    const envOrigins = process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : [];
    return [...defaultOrigins, ...envOrigins];
  }
};

const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = getAllowedOrigins();

    // Permitir requisições sem origin (ex: Postman, aplicações mobile)
    if (!origin) {
      console.log('🔍 CORS: Permitindo requisição sem origin');
      return callback(null, true);
    }

    console.log(`🔍 CORS: Verificando origem: ${origin}`);

    // Em desenvolvimento, ser mais permissivo
    if (process.env.NODE_ENV === 'development') {
      // Permitir localhost e 127.0.0.1 em qualquer porta
      if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
        console.log(`✅ CORS: Origem localhost permitida: ${origin}`);
        return callback(null, true);
      }
    }

    // Verificar se a origem está na lista permitida
    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    }

    // Log de tentativa de acesso não autorizada
    console.warn(`🚫 CORS: Origem não permitida: ${origin}`);
    callback(new Error('Não permitido pelo CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH', 'HEAD'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Forwarded-For',
    'X-Real-IP',
    'X-Forwarded-Proto',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers'
  ],
  exposedHeaders: ['Access-Control-Allow-Origin'],
  maxAge: 86400 // Cache preflight por 24 horas
};

app.use(cors(corsOptions));

// Middleware para garantir headers CORS em todas as respostas
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  next();
});
// Usar sistema de logging customizado
app.use(logger.requestLogger);

// Middleware para garantir respostas JSON consistentes
app.use(ensureJsonResponse);

// Conectar ao PostgreSQL e sincronizar modelos com retry
const initializeDatabase = async () => {
  try {
    logger.info('Iniciando conexão com o banco de dados...');
    await testConnection();
    await syncModels();
    logger.info('Banco de dados inicializado com sucesso!');
  } catch (error) {
    logger.error('Falha crítica na inicialização do banco de dados', {
      error: error.message,
      stack: error.stack
    });
    logger.info('Tentando novamente em 5 segundos...');
    setTimeout(() => {
      process.exit(1);
    }, 5000);
  }
};

// Inicializar banco de dados
initializeDatabase();

// Rotas da API
app.use('/api/auth', authRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/appointments', appointmentRoutes);

// Rota básica para verificar se a API está funcionando
app.get('/api', (req, res) => {
  res.json({ 
    success: true,
    message: 'API MultiAgenda funcionando!',
    timestamp: new Date().toISOString()
  });
});

// Rota para endpoint raiz - evitar vazamento de informações
app.get('/', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Serviço ativo',
    service: 'MultiAgenda API'
  });
});

// Bloquear tentativas de acesso a arquivos sensíveis
app.use(/\.(env|ini|conf|log|bak|backup|sql|zip|tar|gz)$/i, (req, res) => {
  res.status(403).json({ 
    success: false, 
    message: 'Acesso negado' 
  });
});

// Lidar com rotas não encontradas
app.use('*', (req, res) => {
  // Log de tentativas suspeitas (apenas em desenvolvimento)
  if (process.env.NODE_ENV !== 'production') {
    console.log(`Tentativa de acesso à rota não existente: ${req.originalUrl} de ${req.ip}`);
  }
  
  res.status(404).json({ 
    success: false, 
    message: 'Recurso não encontrado' 
  });
});

// Error handler para JSON (deve vir por último)
app.use(jsonErrorHandler);

const PORT = process.env.PORT || 8000;

// Função para inicializar o servidor
async function startServer() {
  try {
    // Testar conexão com banco
    await testConnection();

    // Sincronizar modelos
    await syncModels();

    // Inicializar usuário administrador
    await initializeAdmin();

    // Iniciar servidor
    app.listen(PORT, () => {
      console.log(`🚀 API MultiAgenda rodando em http://localhost:${PORT}/api`);
      console.log(`📊 Ambiente: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔐 Usuário admin inicializado`);
    });

  } catch (error) {
    console.error('💥 Erro ao inicializar servidor:', error.message);
    process.exit(1);
  }
}

// Inicializar servidor
startServer();
